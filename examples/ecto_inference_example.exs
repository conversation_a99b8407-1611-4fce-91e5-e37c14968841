#!/usr/bin/env elixir

# Simple executable example showing Ecto inference with Drops.Operations
# This script demonstrates command operations for creating users using
# Drops.Operations with Ecto schema inference and actual database operations.

# Load the project dependencies
Mix.install([
  {:ecto, "~> 3.10"},
  {:ecto_sql, "~> 3.10"},
  {:ecto_sqlite3, "~> 0.12"},
  {:drops, path: "."}
])

# Configure the test repository for SQLite in-memory database
Application.put_env(:example_app, Example.Repo,
  adapter: Ecto.Adapters.SQLite3,
  database: ":memory:",
  pool: Ecto.Adapters.SQL.Sandbox,
  pool_size: 10,
  queue_target: 5000,
  queue_interval: 1000
)

# Configure Ecto repos
Application.put_env(:example_app, :ecto_repos, [Example.Repo])

# Define the repository
defmodule Example.Repo do
  @moduledoc """
  Example repository using SQLite in-memory database.
  """
  use Ecto.Repo,
    otp_app: :example_app,
    adapter: Ecto.Adapters.SQLite3
end

defmodule Example.User do
  @moduledoc """
  User schema with name and email.
  """
  use Ecto.Schema
  import Ecto.Changeset

  schema "users" do
    field(:name, :string)
    field(:email, :string)

    timestamps()
  end

  @doc """
  Changeset for creating and updating users.
  """
  def changeset(user, attrs) do
    user
    |> cast(attrs, [:name, :email])
    |> validate_required([:name])
    |> validate_format(:email, ~r/@/, message: "must be a valid email")
  end
end

defmodule Example.App do
  @moduledoc """
  Example application using Drops.Operations.
  """
  use Drops.Operations
end

defmodule Example.CreateUser do
  @moduledoc """
  Command operation for creating a user with address using Ecto schema inference.
  """
  use Example.App, :command

  # Use simple schema inference with default options
  schema(Example.User)

  @impl true
  def perform(params) do
    # Actually insert the user to the database
    case Example.Repo.insert(
           Example.User.changeset(%Example.User{}, params)
         ) do
      {:ok, user} ->
        {:ok, %{
          id: user.id,
          name: user.name,
          email: user.email,
          inserted_at: user.inserted_at
        }}
      {:error, changeset} ->
        {:error, changeset}
    end
  end
end

defmodule Example.CreateUserWithValidation do
  @moduledoc """
  Command operation with custom validation rules that also inserts to database.
  """
  use Example.App, :command
  import Drops.Type.DSL

  # Use manual schema with validation rules
  schema do
    %{
      required(:name) => string(:filled?),
      required(:email) => string(:filled?)
    }
  end

  @impl true
  def perform(params) do
    # Insert user to database with custom validation
    case Example.Repo.insert(
           Example.User.changeset(%Example.User{}, params)
         ) do
      {:ok, user} ->
        {:ok, %{
          id: user.id,
          name: user.name,
          email: user.email,
          inserted_at: user.inserted_at
        }}
      {:error, changeset} ->
        {:error, changeset}
    end
  end
end

# Start dependencies and set up database
{:ok, _} = Application.ensure_all_started(:ecto_sql)
{:ok, _} = Example.Repo.start_link()

# Set up the SQL sandbox for clean database state
Ecto.Adapters.SQL.Sandbox.mode(Example.Repo, :manual)
:ok = Ecto.Adapters.SQL.Sandbox.checkout(Example.Repo)

# Create the users table
Ecto.Adapters.SQL.query!(Example.Repo, """
  CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT,
    email TEXT,
    inserted_at DATETIME,
    updated_at DATETIME
  )
""")

# Demonstration script
IO.puts("=== Drops.Operations with Ecto Schema Inference ===")
IO.puts("Using SQLite in-memory database with actual database operations\n")

# Show what fields are available in the schema
IO.puts("User schema fields: #{inspect(Example.User.__schema__(:fields))}\n")

# Show the inferred schema
IO.puts("--- Schema Inference ---")
user_inferred = Drops.Schema.Inference.infer_schema(Example.User, [])
IO.puts("Inferred User schema (default options):")
IO.inspect(user_inferred, pretty: true)

# Test the operation with valid data
IO.puts("\n--- Command Operation Examples ---")

# Valid user data
valid_user_data = %{
  name: "John Doe",
  email: "<EMAIL>"
}

IO.puts("Executing CreateUser operation with valid data:")
case Example.CreateUser.execute(valid_user_data) do
  {:ok, %{result: result, params: params}} ->
    IO.puts("✓ Success:")
    IO.puts("  Validated params: #{inspect(params)}")
    IO.puts("  Created user in database: #{inspect(result)}")

    # Verify the user was actually inserted by querying the database
    user_count = Example.Repo.aggregate(Example.User, :count, :id)
    IO.puts("  Total users in database: #{user_count}")
  {:error, %{result: errors, params: params}} ->
    IO.puts("✗ Operation failed:")
    IO.puts("  Input params: #{inspect(params)}")
    IO.puts("  Errors: #{inspect(errors)}")
end

# Test with additional fields (should be filtered out by schema)
user_data_with_extra = %{
  name: "Jane Smith",
  email: "<EMAIL>",
  age: 28,
  extra_field: "this should be ignored"
}

IO.puts("\nExecuting CreateUser operation with extra fields:")
case Example.CreateUser.execute(user_data_with_extra) do
  {:ok, %{result: result, params: params}} ->
    IO.puts("✓ Success:")
    IO.puts("  Validated params: #{inspect(params)}")
    IO.puts("  Created user in database: #{inspect(result)}")

    # Show current database state
    user_count = Example.Repo.aggregate(Example.User, :count, :id)
    IO.puts("  Total users in database: #{user_count}")
  {:error, %{result: errors, params: params}} ->
    IO.puts("✗ Operation failed:")
    IO.puts("  Input params: #{inspect(params)}")
    IO.puts("  Errors: #{inspect(errors)}")
end

# Test with invalid data using the validation operation
invalid_user_data = %{
  name: "",
  email: ""
}

IO.puts("\nExecuting CreateUserWithValidation operation with invalid data:")
case Example.CreateUserWithValidation.execute(invalid_user_data) do
  {:ok, %{result: result, params: params}} ->
    IO.puts("✓ Success:")
    IO.puts("  Validated params: #{inspect(params)}")
    IO.puts("  Created user: #{inspect(result)}")
  {:error, %{result: errors, params: params}} ->
    IO.puts("✗ Operation failed:")
    IO.puts("  Input params: #{inspect(params)}")
    IO.puts("  Validation errors:")
    if is_list(errors) do
      Enum.each(errors, &IO.puts("    - #{&1}"))
    else
      IO.puts("    - #{inspect(errors)}")
    end
end

# Test with valid data using the validation operation
valid_data_for_validation = %{
  name: "Alice Johnson",
  email: "<EMAIL>"
}

IO.puts("\nExecuting CreateUserWithValidation operation with valid data:")
case Example.CreateUserWithValidation.execute(valid_data_for_validation) do
  {:ok, %{result: result, params: params}} ->
    IO.puts("✓ Success:")
    IO.puts("  Validated params: #{inspect(params)}")
    IO.puts("  Created user in database: #{inspect(result)}")

    # Show final database state
    user_count = Example.Repo.aggregate(Example.User, :count, :id)
    IO.puts("  Total users in database: #{user_count}")
  {:error, %{result: errors, params: params}} ->
    IO.puts("✗ Operation failed:")
    IO.puts("  Input params: #{inspect(params)}")
    IO.puts("  Validation errors:")
    if is_list(errors) do
      Enum.each(errors, &IO.puts("    - #{&1}"))
    else
      IO.puts("    - #{inspect(errors)}")
    end
end

# Show all users in the database
IO.puts("\n--- Database State ---")
all_users = Example.Repo.all(Example.User)
IO.puts("All users in database:")
Enum.each(all_users, fn user ->
  IO.puts("  - ID: #{user.id}, Name: #{user.name}, Email: #{user.email}")
end)

IO.puts("\n--- Key Features Demonstrated ---")
IO.puts("✓ Automatic schema inference from Ecto schema")
IO.puts("✓ Input validation using inferred schema")
IO.puts("✓ Automatic filtering of excluded fields (id, timestamps)")
IO.puts("✓ Clean operation interface with execute/1")
IO.puts("✓ Structured success/failure responses")
IO.puts("✓ Actual database operations with SQLite in-memory")
IO.puts("✓ Real Ecto changesets and validations")
IO.puts("✓ Database transaction handling")

IO.puts("\n=== Example Complete ===")
